-- Company Business Intelligence SQL Queries
-- Human-readable queries for key business insights
-- Each query is well-commented and easy to understand

-- =====================================================
-- SALES PERFORMANCE QUERIES
-- =====================================================

-- 1. Overall Sales Summary
-- Shows total revenue, transactions, and average order value
SELECT 
    COUNT(*) as total_transactions,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as average_order_value,
    SUM(quantity) as total_items_sold,
    MIN(sale_date) as first_sale_date,
    MAX(sale_date) as last_sale_date
FROM sales;

-- 2. Monthly Sales Trends
-- Tracks revenue and transaction count by month
SELECT 
    strftime('%Y-%m', sale_date) as month,
    COUNT(*) as number_of_sales,
    SUM(total_amount) as monthly_revenue,
    AVG(total_amount) as avg_order_value,
    SUM(quantity) as items_sold
FROM sales
GROUP BY strftime('%Y-%m', sale_date)
ORDER BY month;

-- 3. Daily Sales Performance
-- Shows daily sales patterns for operational insights
SELECT 
    sale_date,
    COUNT(*) as daily_transactions,
    SUM(total_amount) as daily_revenue,
    AVG(total_amount) as avg_order_value
FROM sales
GROUP BY sale_date
ORDER BY sale_date DESC
LIMIT 30;

-- =====================================================
-- PRODUCT ANALYSIS QUERIES
-- =====================================================

-- 4. Top Selling Products by Revenue
-- Identifies best performing products for inventory decisions
SELECT 
    p.product_name,
    pc.category_name,
    COUNT(s.sale_id) as times_sold,
    SUM(s.quantity) as total_quantity_sold,
    SUM(s.total_amount) as total_revenue,
    AVG(s.total_amount) as avg_sale_amount
FROM sales s
JOIN products p ON s.product_id = p.product_id
JOIN product_categories pc ON p.category_id = pc.category_id
GROUP BY p.product_id, p.product_name, pc.category_name
ORDER BY total_revenue DESC
LIMIT 20;

-- 5. Product Category Performance
-- Shows which categories generate most revenue
SELECT 
    pc.category_name,
    COUNT(s.sale_id) as number_of_sales,
    SUM(s.quantity) as total_items_sold,
    SUM(s.total_amount) as total_revenue,
    AVG(s.total_amount) as avg_sale_amount,
    pc.margin_percent
FROM sales s
JOIN products p ON s.product_id = p.product_id
JOIN product_categories pc ON p.category_id = pc.category_id
GROUP BY pc.category_id, pc.category_name, pc.margin_percent
ORDER BY total_revenue DESC;

-- 6. Low Stock Alert
-- Identifies products that need restocking
SELECT 
    p.product_name,
    pc.category_name,
    p.stock_quantity,
    p.cost_price,
    p.selling_price,
    p.supplier
FROM products p
JOIN product_categories pc ON p.category_id = pc.category_id
WHERE p.stock_quantity < 50
ORDER BY p.stock_quantity ASC;

-- =====================================================
-- CUSTOMER ANALYSIS QUERIES
-- =====================================================

-- 7. Customer Spending Analysis
-- Shows customer value and purchase behavior
SELECT 
    c.customer_id,
    c.first_name || ' ' || c.last_name as customer_name,
    c.customer_segment,
    COUNT(s.sale_id) as number_of_orders,
    SUM(s.total_amount) as total_spent,
    AVG(s.total_amount) as avg_order_value,
    MIN(s.sale_date) as first_purchase,
    MAX(s.sale_date) as last_purchase
FROM customers c
LEFT JOIN sales s ON c.customer_id = s.customer_id
GROUP BY c.customer_id, c.first_name, c.last_name, c.customer_segment
HAVING COUNT(s.sale_id) > 0
ORDER BY total_spent DESC
LIMIT 50;

-- 8. Customer Segment Performance
-- Compares different customer segments
SELECT 
    c.customer_segment,
    COUNT(DISTINCT c.customer_id) as number_of_customers,
    COUNT(s.sale_id) as total_orders,
    SUM(s.total_amount) as total_revenue,
    AVG(s.total_amount) as avg_order_value,
    SUM(s.total_amount) / COUNT(DISTINCT c.customer_id) as avg_customer_value
FROM customers c
LEFT JOIN sales s ON c.customer_id = s.customer_id
GROUP BY c.customer_segment
ORDER BY total_revenue DESC;

-- 9. Customer Acquisition by Month
-- Shows how many new customers we gain each month
SELECT 
    strftime('%Y-%m', registration_date) as month,
    COUNT(*) as new_customers,
    customer_segment
FROM customers
GROUP BY strftime('%Y-%m', registration_date), customer_segment
ORDER BY month DESC;

-- =====================================================
-- EMPLOYEE PERFORMANCE QUERIES
-- =====================================================

-- 10. Sales Employee Performance
-- Ranks sales team members by performance
SELECT 
    e.employee_id,
    e.first_name || ' ' || e.last_name as employee_name,
    e.position,
    e.salary,
    e.performance_rating,
    COUNT(s.sale_id) as number_of_sales,
    SUM(s.total_amount) as total_sales_revenue,
    AVG(s.total_amount) as avg_sale_amount
FROM employees e
LEFT JOIN sales s ON e.employee_id = s.employee_id
WHERE e.department_id = 1  -- Sales department
GROUP BY e.employee_id, e.first_name, e.last_name, e.position, e.salary, e.performance_rating
HAVING COUNT(s.sale_id) > 0
ORDER BY total_sales_revenue DESC;

-- 11. Department Budget vs Performance
-- Compares department budgets with sales performance
SELECT 
    d.dept_name,
    d.budget,
    COUNT(e.employee_id) as number_of_employees,
    AVG(e.salary) as avg_employee_salary,
    AVG(e.performance_rating) as avg_performance_rating,
    COALESCE(SUM(s.total_amount), 0) as total_sales_generated
FROM departments d
LEFT JOIN employees e ON d.dept_id = e.department_id
LEFT JOIN sales s ON e.employee_id = s.employee_id
GROUP BY d.dept_id, d.dept_name, d.budget
ORDER BY total_sales_generated DESC;

-- =====================================================
-- BUSINESS INTELLIGENCE QUERIES
-- =====================================================

-- 12. Sales Channel Analysis
-- Shows which sales channels perform best
SELECT 
    sales_channel,
    COUNT(*) as number_of_sales,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as avg_order_value,
    SUM(quantity) as total_items_sold
FROM sales
GROUP BY sales_channel
ORDER BY total_revenue DESC;

-- 13. Payment Method Preferences
-- Analyzes customer payment preferences
SELECT 
    payment_method,
    COUNT(*) as number_of_transactions,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as avg_transaction_amount
FROM sales
GROUP BY payment_method
ORDER BY number_of_transactions DESC;

-- 14. Discount Impact Analysis
-- Shows how discounts affect sales volume and revenue
SELECT 
    discount_percent,
    COUNT(*) as number_of_sales,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as avg_order_value,
    SUM(quantity) as total_items_sold
FROM sales
GROUP BY discount_percent
ORDER BY discount_percent;

-- 15. Seasonal Sales Patterns
-- Identifies seasonal trends in sales
SELECT 
    CASE 
        WHEN strftime('%m', sale_date) IN ('12', '01', '02') THEN 'Winter'
        WHEN strftime('%m', sale_date) IN ('03', '04', '05') THEN 'Spring'
        WHEN strftime('%m', sale_date) IN ('06', '07', '08') THEN 'Summer'
        WHEN strftime('%m', sale_date) IN ('09', '10', '11') THEN 'Fall'
    END as season,
    COUNT(*) as number_of_sales,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as avg_order_value
FROM sales
GROUP BY season
ORDER BY total_revenue DESC;
