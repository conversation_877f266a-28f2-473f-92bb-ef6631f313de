# Company Data Analysis Project

A comprehensive, company-level Data Analyst project using Python (pandas, numpy), Excel, and SQL. This project demonstrates real-world data analysis skills with human-readable code and professional reporting.

## 🎯 Project Overview

This project simulates a complete business intelligence solution for a fictional retail company. It includes:

- **Realistic sample data** (5,000+ sales transactions, 1,000+ customers, 500+ products)
- **Python data analysis** using pandas and numpy
- **Professional Excel reports** with formatting and charts
- **SQL queries** for business intelligence
- **Data visualizations** using matplotlib and seaborn
- **Human-readable code** with clear explanations

## 📁 Project Structure

```
DATA_ANALYST/
├── data/                          # CSV data files
│   ├── departments.csv
│   ├── employees.csv
│   ├── products.csv
│   ├── customers.csv
│   ├── product_categories.csv
│   └── sales.csv
├── scripts/                       # Python analysis scripts
│   ├── data_analysis.py          # Main analysis functions
│   └── excel_integration.py      # Excel report generation
├── sql_queries/                   # Business intelligence SQL
│   └── business_insights.sql     # 15+ ready-to-use queries
├── excel_templates/               # Excel templates (future use)
├── output/                        # Generated reports
├── visualizations/               # Charts and graphs
├── docs/                         # Documentation
├── simple_data_generator.py      # Sample data generator
├── main_analysis.py              # Main project runner
├── requirements.txt              # Python dependencies
└── README.md                     # This file
```

## 🚀 Quick Start

### 1. Generate Sample Data
```bash
python simple_data_generator.py
```
This creates realistic company data including:
- 6 departments with budgets
- 150 employees across departments
- 6 product categories
- 500 products with pricing
- 1,000 customers in different segments
- 5,000 sales transactions over 3 years

### 2. Run Complete Analysis
```bash
python main_analysis.py
```
This performs:
- Comprehensive data analysis
- Key business metrics calculation
- Professional visualizations
- Summary report generation

### 3. View Results
- **Summary Report**: `output/summary_report.txt`
- **Visualizations**: `visualizations/` folder
- **Excel Reports**: `output/` folder (when Excel integration runs)

## 📊 Key Features

### Data Analysis Capabilities
- **Sales Performance**: Revenue trends, transaction analysis, seasonal patterns
- **Product Analysis**: Category performance, top-selling products, inventory insights
- **Customer Segmentation**: Spending patterns, customer lifetime value, segment analysis
- **Employee Performance**: Sales team rankings, department comparisons
- **Business Intelligence**: Channel analysis, payment preferences, discount impact

### Professional Reporting
- **Excel Integration**: Formatted reports with charts and styling
- **SQL Queries**: 15+ business intelligence queries ready to use
- **Visualizations**: Professional charts showing key trends
- **Executive Summaries**: High-level insights for stakeholders

### Code Quality
- **Human-Readable**: Clear variable names and extensive comments
- **Modular Design**: Separate classes for different analysis types
- **Error Handling**: Graceful handling of missing data or errors
- **Documentation**: Comprehensive docstrings and explanations

## 🔍 Sample Insights

The analysis provides insights such as:

- **Revenue Trends**: Monthly and seasonal sales patterns
- **Top Performers**: Best-selling products and categories
- **Customer Value**: High-value customers and segment analysis
- **Operational Efficiency**: Sales channel and employee performance
- **Market Opportunities**: Underperforming areas and growth potential

## 📈 SQL Business Intelligence

The project includes ready-to-use SQL queries for:

1. **Sales Performance Queries**
   - Overall sales summary
   - Monthly sales trends
   - Daily performance tracking

2. **Product Analysis Queries**
   - Top-selling products by revenue
   - Category performance comparison
   - Low stock alerts

3. **Customer Analysis Queries**
   - Customer spending analysis
   - Segment performance comparison
   - Customer acquisition trends

4. **Employee Performance Queries**
   - Sales team rankings
   - Department budget vs performance

5. **Business Intelligence Queries**
   - Sales channel analysis
   - Payment method preferences
   - Discount impact analysis
   - Seasonal sales patterns

## 🛠 Technical Requirements

### Python Packages
```
pandas>=2.0.0          # Data manipulation
numpy>=1.24.0          # Numerical computing
matplotlib>=3.7.0      # Basic plotting
seaborn>=0.12.0        # Statistical visualization
openpyxl>=3.1.0        # Excel integration
```

### Optional Packages (for enhanced features)
```
sqlalchemy>=2.0.0      # Database connectivity
plotly>=5.15.0         # Interactive visualizations
jupyter>=1.0.0         # Notebook interface
```

## 📋 Usage Examples

### Basic Analysis
```python
from scripts.data_analysis import CompanyDataAnalyzer

# Initialize analyzer
analyzer = CompanyDataAnalyzer()

# Get sales overview
metrics = analyzer.get_sales_overview()

# Analyze monthly trends
monthly_data = analyzer.analyze_sales_by_month()

# Generate complete report
analyzer.generate_summary_report()
```

### Excel Report Generation
```python
from scripts.excel_integration import ExcelReportGenerator

# Create Excel reports
generator = ExcelReportGenerator()
generator.generate_comprehensive_report()
```

## 🎯 Business Value

This project demonstrates:

1. **Data-Driven Decision Making**: Transform raw data into actionable insights
2. **Professional Reporting**: Create stakeholder-ready reports and visualizations
3. **Scalable Analysis**: Modular code that can handle larger datasets
4. **Business Intelligence**: SQL queries for ongoing analysis needs
5. **Technical Proficiency**: Modern data analysis tools and best practices

## 🔄 Future Enhancements

Potential improvements include:
- Real-time dashboard integration
- Machine learning for sales forecasting
- Advanced customer segmentation
- Automated report scheduling
- Database integration (PostgreSQL, MySQL)
- Web-based dashboard (Streamlit, Dash)

## 📞 Support

For questions or issues:
1. Check the comprehensive comments in the code
2. Review the SQL queries for business intelligence examples
3. Examine the generated reports for expected output formats

## 🏆 Project Highlights

- **Realistic Data**: 5,000+ transactions across multiple business dimensions
- **Professional Quality**: Enterprise-level code structure and documentation
- **Business Focus**: Metrics and insights that matter to stakeholders
- **Easy to Understand**: Human-readable code with clear explanations
- **Complete Workflow**: From data generation to final reporting

This project showcases the complete data analyst workflow from data collection through insight generation and professional reporting.
