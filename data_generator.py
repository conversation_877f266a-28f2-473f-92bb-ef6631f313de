"""
Company Data Generator for Data Analysis Project
Generates realistic sample data for a fictional retail company
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import random
from faker import Faker

# Initialize Faker for generating realistic data
fake = Faker()
np.random.seed(42)  # For reproducible results

class CompanyDataGenerator:
    """Generate realistic company data for analysis"""
    
    def __init__(self):
        self.start_date = datetime(2022, 1, 1)
        self.end_date = datetime(2024, 12, 31)
        
    def generate_departments(self):
        """Generate department data"""
        departments = [
            {'dept_id': 1, 'dept_name': 'Sales', 'budget': 500000},
            {'dept_id': 2, 'dept_name': 'Marketing', 'budget': 300000},
            {'dept_id': 3, 'dept_name': 'IT', 'budget': 400000},
            {'dept_id': 4, 'dept_name': 'HR', 'budget': 200000},
            {'dept_id': 5, 'dept_name': 'Finance', 'budget': 250000},
            {'dept_id': 6, 'dept_name': 'Operations', 'budget': 350000}
        ]
        return pd.DataFrame(departments)
    
    def generate_employees(self, num_employees=150):
        """Generate employee data"""
        employees = []
        
        for i in range(1, num_employees + 1):
            employee = {
                'employee_id': i,
                'first_name': fake.first_name(),
                'last_name': fake.last_name(),
                'email': fake.email(),
                'phone': fake.phone_number(),
                'hire_date': fake.date_between(start_date='-5y', end_date='today'),
                'department_id': random.randint(1, 6),
                'position': random.choice(['Manager', 'Senior', 'Junior', 'Lead', 'Associate']),
                'salary': random.randint(35000, 120000),
                'performance_rating': round(random.uniform(2.5, 5.0), 1)
            }
            employees.append(employee)
            
        return pd.DataFrame(employees)
    
    def generate_product_categories(self):
        """Generate product categories"""
        categories = [
            {'category_id': 1, 'category_name': 'Electronics', 'margin_percent': 25},
            {'category_id': 2, 'category_name': 'Clothing', 'margin_percent': 60},
            {'category_id': 3, 'category_name': 'Home & Garden', 'margin_percent': 45},
            {'category_id': 4, 'category_name': 'Sports & Outdoors', 'margin_percent': 35},
            {'category_id': 5, 'category_name': 'Books & Media', 'margin_percent': 40},
            {'category_id': 6, 'category_name': 'Health & Beauty', 'margin_percent': 55}
        ]
        return pd.DataFrame(categories)
    
    def generate_products(self, num_products=500):
        """Generate product data"""
        products = []
        
        product_names = {
            1: ['Smartphone', 'Laptop', 'Tablet', 'Headphones', 'Smart Watch', 'Camera'],
            2: ['T-Shirt', 'Jeans', 'Dress', 'Jacket', 'Shoes', 'Hat'],
            3: ['Sofa', 'Table', 'Chair', 'Lamp', 'Rug', 'Plant'],
            4: ['Basketball', 'Tennis Racket', 'Yoga Mat', 'Dumbbells', 'Bicycle', 'Helmet'],
            5: ['Novel', 'Textbook', 'Magazine', 'DVD', 'Board Game', 'Puzzle'],
            6: ['Shampoo', 'Moisturizer', 'Perfume', 'Makeup', 'Vitamins', 'Soap']
        }
        
        for i in range(1, num_products + 1):
            category_id = random.randint(1, 6)
            base_name = random.choice(product_names[category_id])
            
            product = {
                'product_id': i,
                'product_name': f"{base_name} {fake.word().title()}",
                'category_id': category_id,
                'cost_price': round(random.uniform(10, 500), 2),
                'selling_price': 0,  # Will calculate based on margin
                'stock_quantity': random.randint(0, 1000),
                'supplier': fake.company(),
                'launch_date': fake.date_between(start_date='-3y', end_date='today')
            }
            
            # Calculate selling price based on category margin
            margin = {1: 1.25, 2: 1.60, 3: 1.45, 4: 1.35, 5: 1.40, 6: 1.55}[category_id]
            product['selling_price'] = round(product['cost_price'] * margin, 2)
            
            products.append(product)
            
        return pd.DataFrame(products)

    def generate_customers(self, num_customers=1000):
        """Generate customer data"""
        customers = []

        for i in range(1, num_customers + 1):
            customer = {
                'customer_id': i,
                'first_name': fake.first_name(),
                'last_name': fake.last_name(),
                'email': fake.email(),
                'phone': fake.phone_number(),
                'address': fake.address().replace('\n', ', '),
                'city': fake.city(),
                'state': fake.state(),
                'zip_code': fake.zipcode(),
                'registration_date': fake.date_between(start_date='-3y', end_date='today'),
                'customer_segment': random.choice(['Premium', 'Regular', 'Budget']),
                'total_spent': 0  # Will be calculated from sales
            }
            customers.append(customer)

        return pd.DataFrame(customers)

    def generate_sales_data(self, customers_df, products_df, employees_df, num_sales=5000):
        """Generate sales transaction data"""
        sales = []

        for i in range(1, num_sales + 1):
            # Random date within our range
            sale_date = fake.date_between(start_date=self.start_date, end_date=self.end_date)

            sale = {
                'sale_id': i,
                'customer_id': random.choice(customers_df['customer_id'].tolist()),
                'product_id': random.choice(products_df['product_id'].tolist()),
                'employee_id': random.choice(employees_df[employees_df['department_id'] == 1]['employee_id'].tolist()),
                'sale_date': sale_date,
                'quantity': random.randint(1, 5),
                'unit_price': 0,  # Will get from products
                'total_amount': 0,  # Will calculate
                'discount_percent': random.choice([0, 5, 10, 15, 20]),
                'payment_method': random.choice(['Credit Card', 'Cash', 'Debit Card', 'Online']),
                'sales_channel': random.choice(['Online', 'In-Store', 'Phone', 'Mobile App'])
            }

            # Get product price
            product_price = products_df[products_df['product_id'] == sale['product_id']]['selling_price'].iloc[0]
            sale['unit_price'] = product_price

            # Calculate total with discount
            subtotal = sale['quantity'] * sale['unit_price']
            discount_amount = subtotal * (sale['discount_percent'] / 100)
            sale['total_amount'] = round(subtotal - discount_amount, 2)

            sales.append(sale)

        return pd.DataFrame(sales)

    def create_database_and_tables(self):
        """Create SQLite database and populate with generated data"""
        print("Generating company data...")

        # Generate all data
        departments_df = self.generate_departments()
        employees_df = self.generate_employees()
        categories_df = self.generate_product_categories()
        products_df = self.generate_products()
        customers_df = self.generate_customers()
        sales_df = self.generate_sales_data(customers_df, products_df, employees_df)

        # Create database connection
        conn = sqlite3.connect('company_database.db')

        # Create tables and insert data
        print("Creating database tables...")
        departments_df.to_sql('departments', conn, if_exists='replace', index=False)
        employees_df.to_sql('employees', conn, if_exists='replace', index=False)
        categories_df.to_sql('product_categories', conn, if_exists='replace', index=False)
        products_df.to_sql('products', conn, if_exists='replace', index=False)
        customers_df.to_sql('customers', conn, if_exists='replace', index=False)
        sales_df.to_sql('sales', conn, if_exists='replace', index=False)

        # Update customer total_spent
        print("Calculating customer totals...")
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE customers
            SET total_spent = (
                SELECT COALESCE(SUM(total_amount), 0)
                FROM sales
                WHERE sales.customer_id = customers.customer_id
            )
        """)
        conn.commit()

        print("Database created successfully!")
        print(f"- Departments: {len(departments_df)} records")
        print(f"- Employees: {len(employees_df)} records")
        print(f"- Product Categories: {len(categories_df)} records")
        print(f"- Products: {len(products_df)} records")
        print(f"- Customers: {len(customers_df)} records")
        print(f"- Sales: {len(sales_df)} records")

        conn.close()

        # Also save as CSV files for Excel integration
        print("\nSaving CSV files for Excel integration...")
        departments_df.to_csv('data/departments.csv', index=False)
        employees_df.to_csv('data/employees.csv', index=False)
        categories_df.to_csv('data/product_categories.csv', index=False)
        products_df.to_csv('data/products.csv', index=False)
        customers_df.to_csv('data/customers.csv', index=False)
        sales_df.to_csv('data/sales.csv', index=False)

        return {
            'departments': departments_df,
            'employees': employees_df,
            'categories': categories_df,
            'products': products_df,
            'customers': customers_df,
            'sales': sales_df
        }

if __name__ == "__main__":
    # Create data directory if it doesn't exist
    import os
    os.makedirs('data', exist_ok=True)

    # Generate all company data
    generator = CompanyDataGenerator()
    data = generator.create_database_and_tables()

    print("\nData generation complete!")
    print("Files created:")
    print("- company_database.db (SQLite database)")
    print("- data/*.csv (CSV files for Excel)")
