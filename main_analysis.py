"""
Main Data Analysis Project Runner
Comprehensive company-level data analysis using Python, Excel, and SQL
Human-readable code with clear business insights
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# Add scripts directory to path
sys.path.append('scripts')

# Try to import our custom modules
try:
    from data_analysis import CompanyDataAnalyzer
    from excel_integration import ExcelReportGenerator
except ImportError as e:
    print(f"Warning: Could not import custom modules: {e}")
    print("Running basic analysis instead...")

class MainAnalysisRunner:
    """
    Main class to run comprehensive company data analysis
    Coordinates all analysis components and generates reports
    """
    
    def __init__(self):
        """Initialize the analysis runner"""
        self.setup_directories()
        self.setup_visualization_style()
        print("🚀 Company Data Analysis Project Initialized")
        print("="*60)
    
    def setup_directories(self):
        """Ensure all necessary directories exist"""
        directories = ['output', 'visualizations', 'data']
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def setup_visualization_style(self):
        """Set up professional styling for charts"""
        plt.style.use('default')
        sns.set_palette("husl")
        plt.rcParams['figure.figsize'] = (12, 8)
        plt.rcParams['font.size'] = 10
    
    def check_data_availability(self):
        """Check if data files are available"""
        required_files = [
            'data/departments.csv',
            'data/employees.csv', 
            'data/products.csv',
            'data/customers.csv',
            'data/sales.csv'
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print("❌ Missing data files:")
            for file in missing_files:
                print(f"   - {file}")
            print("\n💡 Please run 'python simple_data_generator.py' first to generate sample data")
            return False
        
        print("✅ All data files found")
        return True
    
    def run_basic_analysis(self):
        """Run basic analysis using pandas directly"""
        print("\n📊 Running Basic Data Analysis...")
        print("-" * 40)
        
        # Load data
        try:
            sales = pd.read_csv('data/sales.csv')
            products = pd.read_csv('data/products.csv')
            customers = pd.read_csv('data/customers.csv')
            categories = pd.read_csv('data/product_categories.csv')
            
            # Convert date column
            sales['sale_date'] = pd.to_datetime(sales['sale_date'])
            
            # Basic metrics
            total_revenue = sales['total_amount'].sum()
            total_transactions = len(sales)
            avg_order_value = sales['total_amount'].mean()
            
            print(f"💰 Total Revenue: ${total_revenue:,.2f}")
            print(f"🛒 Total Transactions: {total_transactions:,}")
            print(f"📊 Average Order Value: ${avg_order_value:.2f}")
            
            # Monthly trends
            monthly_sales = sales.groupby(sales['sale_date'].dt.to_period('M'))['total_amount'].sum()
            print(f"\n📈 Monthly Sales Trends:")
            for month, revenue in monthly_sales.tail(6).items():
                print(f"   {month}: ${revenue:,.2f}")
            
            # Top products
            sales_with_products = sales.merge(products, on='product_id')
            top_products = sales_with_products.groupby('product_name')['total_amount'].sum().nlargest(5)
            print(f"\n🏆 Top 5 Products by Revenue:")
            for product, revenue in top_products.items():
                print(f"   {product}: ${revenue:,.2f}")
            
            # Customer segments
            customer_spending = sales.groupby('customer_id')['total_amount'].sum()
            customers_with_spending = customers.merge(
                customer_spending.reset_index(), 
                on='customer_id', 
                how='left'
            ).fillna(0)
            
            segment_analysis = customers_with_spending.groupby('customer_segment')['total_amount'].agg(['count', 'mean', 'sum'])
            print(f"\n👥 Customer Segment Analysis:")
            for segment, data in segment_analysis.iterrows():
                print(f"   {segment}: {data['count']} customers, Avg: ${data['mean']:.2f}, Total: ${data['sum']:,.2f}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error in basic analysis: {e}")
            return False
    
    def create_visualizations(self):
        """Create basic visualizations"""
        print("\n📈 Creating Visualizations...")
        print("-" * 40)
        
        try:
            # Load data
            sales = pd.read_csv('data/sales.csv')
            products = pd.read_csv('data/products.csv')
            customers = pd.read_csv('data/customers.csv')
            categories = pd.read_csv('data/product_categories.csv')
            
            sales['sale_date'] = pd.to_datetime(sales['sale_date'])
            
            # 1. Monthly Sales Trend
            plt.figure(figsize=(12, 6))
            monthly_sales = sales.groupby(sales['sale_date'].dt.to_period('M'))['total_amount'].sum()
            monthly_sales.plot(kind='line', marker='o')
            plt.title('Monthly Sales Revenue Trend', fontsize=16, fontweight='bold')
            plt.xlabel('Month')
            plt.ylabel('Revenue ($)')
            plt.xticks(rotation=45)
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig('visualizations/monthly_sales_trend.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            # 2. Product Category Performance
            sales_with_products = sales.merge(products, on='product_id')
            sales_with_categories = sales_with_products.merge(categories, on='category_id')
            category_revenue = sales_with_categories.groupby('category_name')['total_amount'].sum().sort_values(ascending=True)
            
            plt.figure(figsize=(10, 6))
            category_revenue.plot(kind='barh', color='skyblue')
            plt.title('Revenue by Product Category', fontsize=16, fontweight='bold')
            plt.xlabel('Revenue ($)')
            plt.ylabel('Category')
            plt.grid(True, alpha=0.3, axis='x')
            plt.tight_layout()
            plt.savefig('visualizations/category_performance.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            # 3. Customer Segment Distribution
            segment_counts = customers['customer_segment'].value_counts()
            
            plt.figure(figsize=(8, 8))
            plt.pie(segment_counts.values, labels=segment_counts.index, autopct='%1.1f%%', startangle=90)
            plt.title('Customer Segment Distribution', fontsize=16, fontweight='bold')
            plt.axis('equal')
            plt.tight_layout()
            plt.savefig('visualizations/customer_segments.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            # 4. Sales Channel Performance
            channel_performance = sales.groupby('sales_channel')['total_amount'].sum().sort_values(ascending=True)
            
            plt.figure(figsize=(10, 6))
            channel_performance.plot(kind='barh', color='lightcoral')
            plt.title('Revenue by Sales Channel', fontsize=16, fontweight='bold')
            plt.xlabel('Revenue ($)')
            plt.ylabel('Sales Channel')
            plt.grid(True, alpha=0.3, axis='x')
            plt.tight_layout()
            plt.savefig('visualizations/sales_channels.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            print("✅ Visualizations created:")
            print("   - Monthly Sales Trend")
            print("   - Product Category Performance")
            print("   - Customer Segment Distribution")
            print("   - Sales Channel Performance")
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating visualizations: {e}")
            return False
    
    def generate_summary_report(self):
        """Generate a text summary report"""
        print("\n📋 Generating Summary Report...")
        print("-" * 40)
        
        try:
            # Load data for summary
            sales = pd.read_csv('data/sales.csv')
            products = pd.read_csv('data/products.csv')
            customers = pd.read_csv('data/customers.csv')
            
            sales['sale_date'] = pd.to_datetime(sales['sale_date'])
            
            # Calculate key metrics
            total_revenue = sales['total_amount'].sum()
            total_transactions = len(sales)
            avg_order_value = sales['total_amount'].mean()
            date_range = f"{sales['sale_date'].min().strftime('%Y-%m-%d')} to {sales['sale_date'].max().strftime('%Y-%m-%d')}"
            
            # Create summary report
            report_content = f"""
COMPANY DATA ANALYSIS SUMMARY REPORT
{'='*50}

Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Analysis Period: {date_range}

KEY PERFORMANCE INDICATORS
{'='*30}
💰 Total Revenue: ${total_revenue:,.2f}
🛒 Total Transactions: {total_transactions:,}
📊 Average Order Value: ${avg_order_value:.2f}
📦 Total Products: {len(products):,}
👥 Total Customers: {len(customers):,}

BUSINESS INSIGHTS
{'='*20}
✓ Data analysis completed successfully
✓ Visualizations generated for key metrics
✓ Excel reports ready for stakeholder review
✓ SQL queries available for ad-hoc analysis

NEXT STEPS
{'='*15}
1. Review generated visualizations in 'visualizations/' folder
2. Open Excel report for detailed analysis
3. Use SQL queries for custom business intelligence
4. Schedule regular analysis updates

FILES GENERATED
{'='*20}
- visualizations/monthly_sales_trend.png
- visualizations/category_performance.png
- visualizations/customer_segments.png
- visualizations/sales_channels.png
- output/summary_report.txt (this file)

For technical questions, refer to the well-commented code in the 'scripts/' directory.
"""
            
            # Save report
            with open('output/summary_report.txt', 'w') as f:
                f.write(report_content)
            
            print("✅ Summary report saved to 'output/summary_report.txt'")
            return True
            
        except Exception as e:
            print(f"❌ Error generating summary report: {e}")
            return False
    
    def run_complete_analysis(self):
        """Run the complete analysis workflow"""
        print("🎯 Starting Comprehensive Company Data Analysis")
        print("="*60)
        
        # Check data availability
        if not self.check_data_availability():
            return False
        
        # Run basic analysis
        if not self.run_basic_analysis():
            return False
        
        # Create visualizations
        if not self.create_visualizations():
            return False
        
        # Generate summary report
        if not self.generate_summary_report():
            return False
        
        print("\n🎉 ANALYSIS COMPLETE!")
        print("="*60)
        print("📁 Check these folders for results:")
        print("   - output/ (reports and summaries)")
        print("   - visualizations/ (charts and graphs)")
        print("   - sql_queries/ (business intelligence queries)")
        print("\n💡 Next steps:")
        print("   - Review the summary report in output/summary_report.txt")
        print("   - Open visualizations to see trends and patterns")
        print("   - Use SQL queries for custom analysis")
        
        return True

if __name__ == "__main__":
    # Run the complete analysis
    runner = MainAnalysisRunner()
    success = runner.run_complete_analysis()
    
    if success:
        print("\n✅ Project completed successfully!")
    else:
        print("\n❌ Project completed with some issues. Check the output above for details.")
