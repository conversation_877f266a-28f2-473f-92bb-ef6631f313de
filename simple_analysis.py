"""
Simple Company Data Analysis
Uses only built-in Python libraries for basic analysis
Human-readable code with clear business insights
"""

import csv
import os
from datetime import datetime
from collections import defaultdict, Counter

class SimpleCompanyAnalyzer:
    """
    Simple data analyzer using only built-in Python libraries
    Provides key business insights without external dependencies
    """
    
    def __init__(self, data_path='data/'):
        """Initialize analyzer with data path"""
        self.data_path = data_path
        self.data = {}
        self.load_all_data()
        print("✅ Company data loaded successfully!")
    
    def load_all_data(self):
        """Load all CSV files into memory"""
        files_to_load = {
            'departments': 'departments.csv',
            'employees': 'employees.csv',
            'products': 'products.csv',
            'customers': 'customers.csv',
            'categories': 'product_categories.csv',
            'sales': 'sales.csv'
        }
        
        for key, filename in files_to_load.items():
            filepath = os.path.join(self.data_path, filename)
            if os.path.exists(filepath):
                self.data[key] = self.load_csv(filepath)
                print(f"  ✓ Loaded {len(self.data[key])} records from {filename}")
            else:
                print(f"  ❌ File not found: {filename}")
    
    def load_csv(self, filepath):
        """Load CSV file and return list of dictionaries"""
        data = []
        with open(filepath, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                data.append(row)
        return data
    
    def get_sales_overview(self):
        """Calculate key sales metrics"""
        print("\n" + "="*50)
        print("📊 SALES PERFORMANCE OVERVIEW")
        print("="*50)
        
        sales = self.data['sales']
        
        # Calculate basic metrics
        total_revenue = sum(float(sale['total_amount']) for sale in sales)
        total_transactions = len(sales)
        total_items = sum(int(sale['quantity']) for sale in sales)
        avg_order_value = total_revenue / total_transactions if total_transactions > 0 else 0
        
        # Date range
        dates = [sale['sale_date'] for sale in sales]
        start_date = min(dates)
        end_date = max(dates)
        
        print(f"💰 Total Revenue: ${total_revenue:,.2f}")
        print(f"🛒 Total Transactions: {total_transactions:,}")
        print(f"📊 Average Order Value: ${avg_order_value:.2f}")
        print(f"📦 Total Items Sold: {total_items:,}")
        print(f"📅 Period: {start_date} to {end_date}")
        
        return {
            'total_revenue': total_revenue,
            'total_transactions': total_transactions,
            'avg_order_value': avg_order_value,
            'total_items': total_items
        }
    
    def analyze_monthly_sales(self):
        """Analyze sales by month"""
        print("\n" + "="*50)
        print("📈 MONTHLY SALES ANALYSIS")
        print("="*50)
        
        monthly_sales = defaultdict(lambda: {'revenue': 0, 'transactions': 0})
        
        for sale in self.data['sales']:
            # Extract year-month from date (YYYY-MM-DD format)
            month = sale['sale_date'][:7]  # Gets YYYY-MM
            monthly_sales[month]['revenue'] += float(sale['total_amount'])
            monthly_sales[month]['transactions'] += 1
        
        # Sort by month and display
        sorted_months = sorted(monthly_sales.items())
        
        print("📊 Monthly Performance:")
        for month, data in sorted_months[-12:]:  # Last 12 months
            print(f"  {month}: ${data['revenue']:,.2f} ({data['transactions']} transactions)")
        
        # Find best and worst months
        if sorted_months:
            best_month = max(sorted_months, key=lambda x: x[1]['revenue'])
            worst_month = min(sorted_months, key=lambda x: x[1]['revenue'])
            
            print(f"\n🏆 Best Month: {best_month[0]} (${best_month[1]['revenue']:,.2f})")
            print(f"📉 Worst Month: {worst_month[0]} (${worst_month[1]['revenue']:,.2f})")
        
        return monthly_sales
    
    def analyze_product_performance(self):
        """Analyze product and category performance"""
        print("\n" + "="*50)
        print("🛍️ PRODUCT PERFORMANCE ANALYSIS")
        print("="*50)
        
        # Create product lookup
        product_lookup = {p['product_id']: p for p in self.data['products']}
        category_lookup = {c['category_id']: c for c in self.data['categories']}
        
        # Analyze by category
        category_performance = defaultdict(lambda: {'revenue': 0, 'items_sold': 0, 'sales_count': 0})
        product_performance = defaultdict(lambda: {'revenue': 0, 'items_sold': 0})
        
        for sale in self.data['sales']:
            product_id = sale['product_id']
            if product_id in product_lookup:
                product = product_lookup[product_id]
                category_id = product['category_id']
                
                revenue = float(sale['total_amount'])
                quantity = int(sale['quantity'])
                
                # Category stats
                if category_id in category_lookup:
                    category_name = category_lookup[category_id]['category_name']
                    category_performance[category_name]['revenue'] += revenue
                    category_performance[category_name]['items_sold'] += quantity
                    category_performance[category_name]['sales_count'] += 1
                
                # Product stats
                product_name = product['product_name']
                product_performance[product_name]['revenue'] += revenue
                product_performance[product_name]['items_sold'] += quantity
        
        # Display category performance
        print("📊 Category Performance:")
        sorted_categories = sorted(category_performance.items(), key=lambda x: x[1]['revenue'], reverse=True)
        for category, data in sorted_categories:
            print(f"  {category}: ${data['revenue']:,.2f} ({data['items_sold']} items, {data['sales_count']} sales)")
        
        # Display top products
        print(f"\n🏆 Top 10 Products by Revenue:")
        sorted_products = sorted(product_performance.items(), key=lambda x: x[1]['revenue'], reverse=True)
        for i, (product, data) in enumerate(sorted_products[:10], 1):
            print(f"  {i}. {product}: ${data['revenue']:,.2f} ({data['items_sold']} sold)")
        
        return category_performance, product_performance
    
    def analyze_customer_segments(self):
        """Analyze customer behavior by segment"""
        print("\n" + "="*50)
        print("👥 CUSTOMER ANALYSIS")
        print("="*50)
        
        # Calculate customer spending
        customer_spending = defaultdict(lambda: {'total_spent': 0, 'order_count': 0})
        customer_lookup = {c['customer_id']: c for c in self.data['customers']}
        
        for sale in self.data['sales']:
            customer_id = sale['customer_id']
            customer_spending[customer_id]['total_spent'] += float(sale['total_amount'])
            customer_spending[customer_id]['order_count'] += 1
        
        # Analyze by segment
        segment_analysis = defaultdict(lambda: {'customers': 0, 'total_revenue': 0, 'total_orders': 0})
        
        for customer_id, spending in customer_spending.items():
            if customer_id in customer_lookup:
                segment = customer_lookup[customer_id]['customer_segment']
                segment_analysis[segment]['customers'] += 1
                segment_analysis[segment]['total_revenue'] += spending['total_spent']
                segment_analysis[segment]['total_orders'] += spending['order_count']
        
        print("📊 Customer Segment Analysis:")
        for segment, data in segment_analysis.items():
            avg_spent = data['total_revenue'] / data['customers'] if data['customers'] > 0 else 0
            avg_orders = data['total_orders'] / data['customers'] if data['customers'] > 0 else 0
            
            print(f"  {segment} Customers:")
            print(f"    - Count: {data['customers']}")
            print(f"    - Total Revenue: ${data['total_revenue']:,.2f}")
            print(f"    - Average Spent: ${avg_spent:.2f}")
            print(f"    - Average Orders: {avg_orders:.1f}")
        
        # Top customers
        top_customers = sorted(customer_spending.items(), key=lambda x: x[1]['total_spent'], reverse=True)
        print(f"\n💎 Top 10 Customers by Spending:")
        for i, (customer_id, data) in enumerate(top_customers[:10], 1):
            customer_name = "Unknown"
            if customer_id in customer_lookup:
                customer = customer_lookup[customer_id]
                customer_name = f"{customer['first_name']} {customer['last_name']}"
            print(f"  {i}. {customer_name} (ID: {customer_id}): ${data['total_spent']:,.2f} ({data['order_count']} orders)")
        
        return segment_analysis
    
    def analyze_sales_channels(self):
        """Analyze performance by sales channel"""
        print("\n" + "="*50)
        print("🛒 SALES CHANNEL ANALYSIS")
        print("="*50)
        
        channel_performance = defaultdict(lambda: {'revenue': 0, 'transactions': 0, 'items': 0})
        
        for sale in self.data['sales']:
            channel = sale['sales_channel']
            channel_performance[channel]['revenue'] += float(sale['total_amount'])
            channel_performance[channel]['transactions'] += 1
            channel_performance[channel]['items'] += int(sale['quantity'])
        
        print("📊 Channel Performance:")
        sorted_channels = sorted(channel_performance.items(), key=lambda x: x[1]['revenue'], reverse=True)
        for channel, data in sorted_channels:
            avg_order = data['revenue'] / data['transactions'] if data['transactions'] > 0 else 0
            print(f"  {channel}:")
            print(f"    - Revenue: ${data['revenue']:,.2f}")
            print(f"    - Transactions: {data['transactions']:,}")
            print(f"    - Average Order: ${avg_order:.2f}")
        
        return channel_performance
    
    def generate_summary_report(self):
        """Generate comprehensive summary report"""
        print("\n" + "="*60)
        print("📋 GENERATING EXECUTIVE SUMMARY")
        print("="*60)
        
        # Run all analyses
        sales_overview = self.get_sales_overview()
        monthly_sales = self.analyze_monthly_sales()
        category_perf, product_perf = self.analyze_product_performance()
        customer_analysis = self.analyze_customer_segments()
        channel_analysis = self.analyze_sales_channels()
        
        # Create summary report
        report_content = f"""
COMPANY DATA ANALYSIS EXECUTIVE SUMMARY
{'='*50}

Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

KEY PERFORMANCE INDICATORS
{'='*30}
💰 Total Revenue: ${sales_overview['total_revenue']:,.2f}
🛒 Total Transactions: {sales_overview['total_transactions']:,}
📊 Average Order Value: ${sales_overview['avg_order_value']:.2f}
📦 Total Items Sold: {sales_overview['total_items']:,}

BUSINESS INSIGHTS
{'='*20}
✓ {len(self.data['products'])} products across {len(self.data['categories'])} categories
✓ {len(self.data['customers'])} customers in {len(customer_analysis)} segments
✓ {len(self.data['employees'])} employees across {len(self.data['departments'])} departments
✓ Sales data spanning multiple years with seasonal trends

TOP PERFORMERS
{'='*15}
🏆 Best Product Category: {max(category_perf.items(), key=lambda x: x[1]['revenue'])[0] if category_perf else 'N/A'}
🏆 Best Sales Channel: {max(channel_analysis.items(), key=lambda x: x[1]['revenue'])[0] if channel_analysis else 'N/A'}
🏆 Most Valuable Segment: {max(customer_analysis.items(), key=lambda x: x[1]['total_revenue'])[0] if customer_analysis else 'N/A'}

RECOMMENDATIONS
{'='*15}
1. Focus marketing efforts on top-performing product categories
2. Optimize inventory for best-selling products
3. Develop retention strategies for high-value customer segments
4. Invest in most profitable sales channels

FILES AVAILABLE
{'='*15}
- Data files in 'data/' directory (CSV format)
- SQL queries in 'sql_queries/' directory
- Python scripts in 'scripts/' directory
- This summary in 'output/' directory

For detailed analysis, use the Python scripts with pandas/numpy when packages are installed.
"""
        
        # Save report
        os.makedirs('output', exist_ok=True)
        with open('output/executive_summary.txt', 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print("✅ Executive summary saved to 'output/executive_summary.txt'")
        print("✅ Analysis complete! Key insights generated.")
        
        return report_content

if __name__ == "__main__":
    print("🚀 Starting Simple Company Data Analysis")
    print("="*60)
    
    # Check if data exists
    if not os.path.exists('data/sales.csv'):
        print("❌ Data files not found!")
        print("💡 Please run 'python simple_data_generator.py' first")
    else:
        # Run analysis
        analyzer = SimpleCompanyAnalyzer()
        analyzer.generate_summary_report()
        
        print("\n🎉 ANALYSIS COMPLETE!")
        print("="*60)
        print("📁 Results saved in:")
        print("   - output/executive_summary.txt")
        print("\n💡 Next steps:")
        print("   - Install pandas/numpy for advanced analysis")
        print("   - Use SQL queries for custom insights")
        print("   - Review generated summary report")
