"""
Excel Integration Script
Creates professional Excel reports from company data
Human-readable code with clear formatting and charts
"""

import pandas as pd
import numpy as np
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment, Border, Side
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference
from datetime import datetime
import os

class ExcelReportGenerator:
    """
    Creates professional Excel reports with formatting and charts
    Makes data easy to understand for business stakeholders
    """
    
    def __init__(self, data_path='data/'):
        """Initialize with data from CSV files"""
        self.data_path = data_path
        self.load_data()
        print("✓ Data loaded for Excel report generation")
    
    def load_data(self):
        """Load all CSV data into pandas DataFrames"""
        self.departments = pd.read_csv(f'{self.data_path}departments.csv')
        self.employees = pd.read_csv(f'{self.data_path}employees.csv')
        self.product_categories = pd.read_csv(f'{self.data_path}product_categories.csv')
        self.products = pd.read_csv(f'{self.data_path}products.csv')
        self.customers = pd.read_csv(f'{self.data_path}customers.csv')
        self.sales = pd.read_csv(f'{self.data_path}sales.csv')
        
        # Convert date columns
        self.sales['sale_date'] = pd.to_datetime(self.sales['sale_date'])
        self.employees['hire_date'] = pd.to_datetime(self.employees['hire_date'])
        self.customers['registration_date'] = pd.to_datetime(self.customers['registration_date'])
    
    def create_sales_summary_sheet(self, workbook):
        """Create a sales summary worksheet with key metrics"""
        ws = workbook.create_sheet("Sales Summary")
        
        # Title
        ws['A1'] = "SALES PERFORMANCE SUMMARY"
        ws['A1'].font = Font(size=16, bold=True)
        ws['A1'].fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        ws['A1'].font = Font(size=16, bold=True, color="FFFFFF")
        ws.merge_cells('A1:D1')
        
        # Key metrics
        total_revenue = self.sales['total_amount'].sum()
        total_transactions = len(self.sales)
        avg_order_value = self.sales['total_amount'].mean()
        total_items = self.sales['quantity'].sum()
        
        # Add metrics with formatting
        metrics = [
            ("Total Revenue", f"${total_revenue:,.2f}"),
            ("Total Transactions", f"{total_transactions:,}"),
            ("Average Order Value", f"${avg_order_value:.2f}"),
            ("Total Items Sold", f"{total_items:,}"),
            ("Report Date", datetime.now().strftime("%Y-%m-%d"))
        ]
        
        row = 3
        for metric, value in metrics:
            ws[f'A{row}'] = metric
            ws[f'B{row}'] = value
            ws[f'A{row}'].font = Font(bold=True)
            row += 1
        
        # Monthly sales data
        ws['A9'] = "MONTHLY SALES BREAKDOWN"
        ws['A9'].font = Font(size=14, bold=True)
        
        # Calculate monthly sales
        self.sales['month_year'] = self.sales['sale_date'].dt.to_period('M')
        monthly_sales = self.sales.groupby('month_year').agg({
            'total_amount': 'sum',
            'sale_id': 'count'
        }).round(2)
        
        # Headers
        headers = ['Month', 'Revenue', 'Transactions']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=11, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
        
        # Data
        row = 12
        for month, data in monthly_sales.iterrows():
            ws.cell(row=row, column=1, value=str(month))
            ws.cell(row=row, column=2, value=f"${data['total_amount']:,.2f}")
            ws.cell(row=row, column=3, value=data['sale_id'])
            row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def create_product_analysis_sheet(self, workbook):
        """Create product performance analysis worksheet"""
        ws = workbook.create_sheet("Product Analysis")
        
        # Title
        ws['A1'] = "PRODUCT PERFORMANCE ANALYSIS"
        ws['A1'].font = Font(size=16, bold=True)
        ws['A1'].fill = PatternFill(start_color="70AD47", end_color="70AD47", fill_type="solid")
        ws['A1'].font = Font(size=16, bold=True, color="FFFFFF")
        ws.merge_cells('A1:E1')
        
        # Category performance
        sales_with_products = self.sales.merge(self.products, on='product_id')
        sales_with_categories = sales_with_products.merge(self.product_categories, on='category_id')
        
        category_performance = sales_with_categories.groupby('category_name').agg({
            'total_amount': 'sum',
            'quantity': 'sum',
            'sale_id': 'count'
        }).round(2).sort_values('total_amount', ascending=False)
        
        # Headers for category analysis
        ws['A3'] = "CATEGORY PERFORMANCE"
        ws['A3'].font = Font(size=14, bold=True)
        
        headers = ['Category', 'Revenue', 'Items Sold', 'Number of Sales']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=5, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
        
        # Category data
        row = 6
        for category, data in category_performance.iterrows():
            ws.cell(row=row, column=1, value=category)
            ws.cell(row=row, column=2, value=f"${data['total_amount']:,.2f}")
            ws.cell(row=row, column=3, value=data['quantity'])
            ws.cell(row=row, column=4, value=data['sale_id'])
            row += 1
        
        # Top products
        product_performance = sales_with_products.groupby('product_name').agg({
            'total_amount': 'sum',
            'quantity': 'sum'
        }).round(2).sort_values('total_amount', ascending=False).head(15)
        
        ws[f'A{row+2}'] = "TOP 15 PRODUCTS BY REVENUE"
        ws[f'A{row+2}'].font = Font(size=14, bold=True)
        
        headers = ['Product Name', 'Revenue', 'Items Sold']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row+4, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
        
        # Product data
        start_row = row + 5
        for idx, (product, data) in enumerate(product_performance.iterrows()):
            current_row = start_row + idx
            ws.cell(row=current_row, column=1, value=product)
            ws.cell(row=current_row, column=2, value=f"${data['total_amount']:,.2f}")
            ws.cell(row=current_row, column=3, value=data['quantity'])
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def create_customer_analysis_sheet(self, workbook):
        """Create customer analysis worksheet"""
        ws = workbook.create_sheet("Customer Analysis")
        
        # Title
        ws['A1'] = "CUSTOMER ANALYSIS"
        ws['A1'].font = Font(size=16, bold=True)
        ws['A1'].fill = PatternFill(start_color="C5504B", end_color="C5504B", fill_type="solid")
        ws['A1'].font = Font(size=16, bold=True, color="FFFFFF")
        ws.merge_cells('A1:F1')
        
        # Customer segment analysis
        customer_spending = self.sales.groupby('customer_id').agg({
            'total_amount': 'sum',
            'sale_id': 'count'
        }).round(2)
        
        customer_analysis = customer_spending.merge(
            self.customers[['customer_id', 'customer_segment']], 
            on='customer_id'
        )
        
        segment_summary = customer_analysis.groupby('customer_segment').agg({
            'total_amount': ['mean', 'sum', 'count'],
            'sale_id': 'mean'
        }).round(2)
        
        # Headers for segment analysis
        ws['A3'] = "CUSTOMER SEGMENT SUMMARY"
        ws['A3'].font = Font(size=14, bold=True)
        
        headers = ['Segment', 'Customers', 'Total Revenue', 'Avg Spent', 'Avg Orders']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=5, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="F2DCDB", end_color="F2DCDB", fill_type="solid")
        
        # Segment data
        row = 6
        for segment in customer_analysis['customer_segment'].unique():
            segment_data = customer_analysis[customer_analysis['customer_segment'] == segment]
            ws.cell(row=row, column=1, value=segment)
            ws.cell(row=row, column=2, value=len(segment_data))
            ws.cell(row=row, column=3, value=f"${segment_data['total_amount'].sum():,.2f}")
            ws.cell(row=row, column=4, value=f"${segment_data['total_amount'].mean():.2f}")
            ws.cell(row=row, column=5, value=f"{segment_data['sale_id'].mean():.1f}")
            row += 1
        
        # Top customers
        top_customers = customer_analysis.nlargest(20, 'total_amount')
        
        ws[f'A{row+2}'] = "TOP 20 CUSTOMERS BY SPENDING"
        ws[f'A{row+2}'].font = Font(size=14, bold=True)
        
        headers = ['Customer ID', 'Segment', 'Total Spent', 'Number of Orders']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row+4, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="F2DCDB", end_color="F2DCDB", fill_type="solid")
        
        # Customer data
        start_row = row + 5
        for idx, (customer_id, data) in enumerate(top_customers.iterrows()):
            current_row = start_row + idx
            ws.cell(row=current_row, column=1, value=customer_id)
            ws.cell(row=current_row, column=2, value=data['customer_segment'])
            ws.cell(row=current_row, column=3, value=f"${data['total_amount']:,.2f}")
            ws.cell(row=current_row, column=4, value=data['sale_id'])
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def generate_comprehensive_report(self, filename='output/company_analysis_report.xlsx'):
        """Generate a comprehensive Excel report with all analyses"""
        print("📊 Generating comprehensive Excel report...")
        
        # Create workbook
        workbook = Workbook()
        
        # Remove default sheet
        workbook.remove(workbook.active)
        
        # Create all analysis sheets
        self.create_sales_summary_sheet(workbook)
        self.create_product_analysis_sheet(workbook)
        self.create_customer_analysis_sheet(workbook)
        
        # Save the workbook
        os.makedirs('output', exist_ok=True)
        workbook.save(filename)
        
        print(f"✅ Excel report saved as '{filename}'")
        print("📋 Report includes:")
        print("   - Sales Summary with key metrics")
        print("   - Product Performance Analysis")
        print("   - Customer Segment Analysis")
        print("   - Professional formatting and styling")
        
        return filename

if __name__ == "__main__":
    # Generate Excel report
    generator = ExcelReportGenerator()
    generator.generate_comprehensive_report()
