"""
Simple Company Data Generator using only built-in Python libraries
Creates CSV files with realistic company data
"""

import csv
import random
import sqlite3
from datetime import datetime, timedelta

class SimpleDataGenerator:
    """Generate realistic company data using built-in libraries"""
    
    def __init__(self):
        self.start_date = datetime(2022, 1, 1)
        self.end_date = datetime(2024, 12, 31)
        
        # Sample data for realistic generation
        self.first_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
        self.last_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
        self.cities = ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose']
        self.states = ['NY', 'CA', 'IL', 'TX', 'AZ', 'PA', 'TX', 'CA', 'TX', 'CA']
        
    def random_date(self, start, end):
        """Generate random date between start and end"""
        time_between = end - start
        days_between = time_between.days
        random_days = random.randrange(days_between)
        return start + timedelta(days=random_days)
    
    def generate_departments(self):
        """Generate department data"""
        departments = [
            [1, 'Sales', 500000],
            [2, 'Marketing', 300000],
            [3, 'IT', 400000],
            [4, 'HR', 200000],
            [5, 'Finance', 250000],
            [6, 'Operations', 350000]
        ]
        
        with open('data/departments.csv', 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(['dept_id', 'dept_name', 'budget'])
            writer.writerows(departments)
        
        return departments
    
    def generate_employees(self, num_employees=150):
        """Generate employee data"""
        employees = []
        
        with open('data/employees.csv', 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(['employee_id', 'first_name', 'last_name', 'email', 'phone', 
                           'hire_date', 'department_id', 'position', 'salary', 'performance_rating'])
            
            for i in range(1, num_employees + 1):
                first_name = random.choice(self.first_names)
                last_name = random.choice(self.last_names)
                email = f"{first_name.lower()}.{last_name.lower()}@company.com"
                phone = f"({random.randint(100,999)}) {random.randint(100,999)}-{random.randint(1000,9999)}"
                hire_date = self.random_date(datetime(2019, 1, 1), datetime(2024, 1, 1)).strftime('%Y-%m-%d')
                dept_id = random.randint(1, 6)
                position = random.choice(['Manager', 'Senior', 'Junior', 'Lead', 'Associate'])
                salary = random.randint(35000, 120000)
                rating = round(random.uniform(2.5, 5.0), 1)
                
                employee = [i, first_name, last_name, email, phone, hire_date, dept_id, position, salary, rating]
                employees.append(employee)
                writer.writerow(employee)
        
        return employees
    
    def generate_product_categories(self):
        """Generate product categories"""
        categories = [
            [1, 'Electronics', 25],
            [2, 'Clothing', 60],
            [3, 'Home & Garden', 45],
            [4, 'Sports & Outdoors', 35],
            [5, 'Books & Media', 40],
            [6, 'Health & Beauty', 55]
        ]
        
        with open('data/product_categories.csv', 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(['category_id', 'category_name', 'margin_percent'])
            writer.writerows(categories)
        
        return categories
    
    def generate_products(self, num_products=500):
        """Generate product data"""
        product_names = {
            1: ['Smartphone Pro', 'Laptop Ultra', 'Tablet Max', 'Headphones Elite', 'Smart Watch', 'Camera Digital'],
            2: ['T-Shirt Premium', 'Jeans Classic', 'Dress Elegant', 'Jacket Winter', 'Shoes Sport', 'Hat Casual'],
            3: ['Sofa Comfort', 'Table Oak', 'Chair Ergonomic', 'Lamp LED', 'Rug Persian', 'Plant Indoor'],
            4: ['Basketball Pro', 'Tennis Racket', 'Yoga Mat Premium', 'Dumbbells Set', 'Bicycle Mountain', 'Helmet Safety'],
            5: ['Novel Bestseller', 'Textbook Science', 'Magazine Monthly', 'DVD Collection', 'Board Game Family', 'Puzzle 1000pc'],
            6: ['Shampoo Organic', 'Moisturizer Daily', 'Perfume Luxury', 'Makeup Kit', 'Vitamins Multi', 'Soap Natural']
        }
        
        companies = ['TechCorp', 'StyleCo', 'HomeMax', 'SportsPro', 'MediaPlus', 'BeautyBest']
        
        with open('data/products.csv', 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(['product_id', 'product_name', 'category_id', 'cost_price', 'selling_price', 
                           'stock_quantity', 'supplier', 'launch_date'])
            
            for i in range(1, num_products + 1):
                category_id = random.randint(1, 6)
                product_name = random.choice(product_names[category_id])
                cost_price = round(random.uniform(10, 500), 2)
                
                # Calculate selling price based on category margin
                margins = {1: 1.25, 2: 1.60, 3: 1.45, 4: 1.35, 5: 1.40, 6: 1.55}
                selling_price = round(cost_price * margins[category_id], 2)
                
                stock_quantity = random.randint(0, 1000)
                supplier = random.choice(companies)
                launch_date = self.random_date(datetime(2021, 1, 1), datetime(2024, 1, 1)).strftime('%Y-%m-%d')
                
                product = [i, product_name, category_id, cost_price, selling_price, stock_quantity, supplier, launch_date]
                writer.writerow(product)
        
        print(f"Generated {num_products} products")
    
    def generate_customers(self, num_customers=1000):
        """Generate customer data"""
        with open('data/customers.csv', 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(['customer_id', 'first_name', 'last_name', 'email', 'phone', 'address', 
                           'city', 'state', 'zip_code', 'registration_date', 'customer_segment', 'total_spent'])
            
            for i in range(1, num_customers + 1):
                first_name = random.choice(self.first_names)
                last_name = random.choice(self.last_names)
                email = f"{first_name.lower()}.{last_name.lower()}{i}@email.com"
                phone = f"({random.randint(100,999)}) {random.randint(100,999)}-{random.randint(1000,9999)}"
                address = f"{random.randint(100, 9999)} {random.choice(['Main', 'Oak', 'Pine', 'Elm'])} St"
                city_idx = random.randint(0, len(self.cities)-1)
                city = self.cities[city_idx]
                state = self.states[city_idx]
                zip_code = f"{random.randint(10000, 99999)}"
                reg_date = self.random_date(datetime(2021, 1, 1), datetime(2024, 1, 1)).strftime('%Y-%m-%d')
                segment = random.choice(['Premium', 'Regular', 'Budget'])
                total_spent = 0  # Will be calculated later
                
                customer = [i, first_name, last_name, email, phone, address, city, state, zip_code, reg_date, segment, total_spent]
                writer.writerow(customer)
        
        print(f"Generated {num_customers} customers")
    
    def generate_sales_data(self, num_sales=5000):
        """Generate sales transaction data"""
        with open('data/sales.csv', 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(['sale_id', 'customer_id', 'product_id', 'employee_id', 'sale_date', 
                           'quantity', 'unit_price', 'total_amount', 'discount_percent', 
                           'payment_method', 'sales_channel'])
            
            for i in range(1, num_sales + 1):
                customer_id = random.randint(1, 1000)
                product_id = random.randint(1, 500)
                employee_id = random.randint(1, 25)  # Sales employees only
                sale_date = self.random_date(self.start_date, self.end_date).strftime('%Y-%m-%d')
                quantity = random.randint(1, 5)
                unit_price = round(random.uniform(15, 800), 2)  # Approximate product prices
                discount_percent = random.choice([0, 5, 10, 15, 20])
                payment_method = random.choice(['Credit Card', 'Cash', 'Debit Card', 'Online'])
                sales_channel = random.choice(['Online', 'In-Store', 'Phone', 'Mobile App'])
                
                # Calculate total with discount
                subtotal = quantity * unit_price
                discount_amount = subtotal * (discount_percent / 100)
                total_amount = round(subtotal - discount_amount, 2)
                
                sale = [i, customer_id, product_id, employee_id, sale_date, quantity, unit_price, 
                       total_amount, discount_percent, payment_method, sales_channel]
                writer.writerow(sale)
        
        print(f"Generated {num_sales} sales transactions")
    
    def create_all_data(self):
        """Generate all company data"""
        print("Generating company data...")
        
        self.generate_departments()
        print("✓ Departments created")
        
        self.generate_employees()
        print("✓ Employees created")
        
        self.generate_product_categories()
        print("✓ Product categories created")
        
        self.generate_products()
        print("✓ Products created")
        
        self.generate_customers()
        print("✓ Customers created")
        
        self.generate_sales_data()
        print("✓ Sales data created")
        
        print("\nAll data files created successfully in the 'data' directory!")
        print("Files created:")
        print("- departments.csv")
        print("- employees.csv") 
        print("- product_categories.csv")
        print("- products.csv")
        print("- customers.csv")
        print("- sales.csv")

if __name__ == "__main__":
    generator = SimpleDataGenerator()
    generator.create_all_data()
