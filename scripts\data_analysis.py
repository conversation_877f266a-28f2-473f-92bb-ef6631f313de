"""
Company Data Analysis Script
Comprehensive analysis of company data using pandas and numpy
Human-readable code with clear explanations
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class CompanyDataAnalyzer:
    """
    Main class for analyzing company data
    Provides easy-to-understand methods for business insights
    """
    
    def __init__(self, data_path='data/'):
        """
        Initialize the analyzer with data from CSV files
        
        Args:
            data_path (str): Path to the directory containing CSV files
        """
        self.data_path = data_path
        self.load_all_data()
        print("✓ Company data loaded successfully!")
        print(f"  - {len(self.departments)} departments")
        print(f"  - {len(self.employees)} employees") 
        print(f"  - {len(self.products)} products")
        print(f"  - {len(self.customers)} customers")
        print(f"  - {len(self.sales)} sales transactions")
    
    def load_all_data(self):
        """Load all CSV files into pandas DataFrames"""
        try:
            # Load each dataset
            self.departments = pd.read_csv(f'{self.data_path}departments.csv')
            self.employees = pd.read_csv(f'{self.data_path}employees.csv')
            self.product_categories = pd.read_csv(f'{self.data_path}product_categories.csv')
            self.products = pd.read_csv(f'{self.data_path}products.csv')
            self.customers = pd.read_csv(f'{self.data_path}customers.csv')
            self.sales = pd.read_csv(f'{self.data_path}sales.csv')
            
            # Convert date columns to datetime
            self.employees['hire_date'] = pd.to_datetime(self.employees['hire_date'])
            self.products['launch_date'] = pd.to_datetime(self.products['launch_date'])
            self.customers['registration_date'] = pd.to_datetime(self.customers['registration_date'])
            self.sales['sale_date'] = pd.to_datetime(self.sales['sale_date'])
            
        except FileNotFoundError as e:
            print(f"Error: Could not find data files. Please run the data generator first.")
            raise e
    
    def get_sales_overview(self):
        """
        Get a comprehensive overview of sales performance
        Returns key metrics that business stakeholders care about
        """
        print("\n" + "="*50)
        print("SALES PERFORMANCE OVERVIEW")
        print("="*50)
        
        # Calculate key metrics
        total_revenue = self.sales['total_amount'].sum()
        total_transactions = len(self.sales)
        average_order_value = self.sales['total_amount'].mean()
        total_items_sold = self.sales['quantity'].sum()
        
        # Date range analysis
        start_date = self.sales['sale_date'].min()
        end_date = self.sales['sale_date'].max()
        days_in_period = (end_date - start_date).days + 1
        daily_average_revenue = total_revenue / days_in_period
        
        # Display results in human-readable format
        print(f"📊 Total Revenue: ${total_revenue:,.2f}")
        print(f"🛒 Total Transactions: {total_transactions:,}")
        print(f"💰 Average Order Value: ${average_order_value:.2f}")
        print(f"📦 Total Items Sold: {total_items_sold:,}")
        print(f"📅 Analysis Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        print(f"📈 Daily Average Revenue: ${daily_average_revenue:.2f}")
        
        return {
            'total_revenue': total_revenue,
            'total_transactions': total_transactions,
            'average_order_value': average_order_value,
            'total_items_sold': total_items_sold,
            'daily_average_revenue': daily_average_revenue
        }
    
    def analyze_sales_by_month(self):
        """
        Analyze sales trends by month
        Shows seasonal patterns and growth trends
        """
        print("\n" + "="*50)
        print("MONTHLY SALES ANALYSIS")
        print("="*50)
        
        # Create month-year column for grouping
        self.sales['month_year'] = self.sales['sale_date'].dt.to_period('M')
        
        # Group sales by month
        monthly_sales = self.sales.groupby('month_year').agg({
            'total_amount': ['sum', 'count', 'mean'],
            'quantity': 'sum'
        }).round(2)
        
        # Flatten column names for easier reading
        monthly_sales.columns = ['Total_Revenue', 'Number_of_Sales', 'Average_Order_Value', 'Items_Sold']
        
        # Display top and bottom performing months
        print("🏆 TOP 5 PERFORMING MONTHS (by revenue):")
        top_months = monthly_sales.nlargest(5, 'Total_Revenue')
        for month, row in top_months.iterrows():
            print(f"  {month}: ${row['Total_Revenue']:,.2f} ({row['Number_of_Sales']} transactions)")
        
        print("\n📉 BOTTOM 5 PERFORMING MONTHS (by revenue):")
        bottom_months = monthly_sales.nsmallest(5, 'Total_Revenue')
        for month, row in bottom_months.iterrows():
            print(f"  {month}: ${row['Total_Revenue']:,.2f} ({row['Number_of_Sales']} transactions)")
        
        return monthly_sales
    
    def analyze_product_performance(self):
        """
        Analyze which products and categories perform best
        Helps with inventory and marketing decisions
        """
        print("\n" + "="*50)
        print("PRODUCT PERFORMANCE ANALYSIS")
        print("="*50)
        
        # Merge sales with product and category information
        sales_with_products = self.sales.merge(self.products, on='product_id')
        sales_with_categories = sales_with_products.merge(self.product_categories, on='category_id')
        
        # Analyze by product category
        category_performance = sales_with_categories.groupby('category_name').agg({
            'total_amount': 'sum',
            'quantity': 'sum',
            'sale_id': 'count'
        }).round(2)
        
        category_performance.columns = ['Total_Revenue', 'Items_Sold', 'Number_of_Sales']
        category_performance = category_performance.sort_values('Total_Revenue', ascending=False)
        
        print("📊 SALES BY PRODUCT CATEGORY:")
        for category, row in category_performance.iterrows():
            print(f"  {category}: ${row['Total_Revenue']:,.2f} ({row['Items_Sold']} items, {row['Number_of_Sales']} sales)")
        
        # Top selling individual products
        product_performance = sales_with_products.groupby('product_name').agg({
            'total_amount': 'sum',
            'quantity': 'sum'
        }).round(2)
        
        product_performance.columns = ['Total_Revenue', 'Items_Sold']
        top_products = product_performance.nlargest(10, 'Total_Revenue')
        
        print("\n🏆 TOP 10 PRODUCTS BY REVENUE:")
        for product, row in top_products.iterrows():
            print(f"  {product}: ${row['Total_Revenue']:,.2f} ({row['Items_Sold']} sold)")
        
        return category_performance, product_performance
    
    def analyze_customer_segments(self):
        """
        Analyze customer behavior and segments
        Helps with customer retention and marketing strategies
        """
        print("\n" + "="*50)
        print("CUSTOMER ANALYSIS")
        print("="*50)
        
        # Calculate customer spending
        customer_spending = self.sales.groupby('customer_id').agg({
            'total_amount': 'sum',
            'sale_id': 'count',
            'sale_date': ['min', 'max']
        }).round(2)
        
        # Flatten column names
        customer_spending.columns = ['Total_Spent', 'Number_of_Orders', 'First_Purchase', 'Last_Purchase']
        
        # Merge with customer information
        customer_analysis = customer_spending.merge(
            self.customers[['customer_id', 'customer_segment', 'registration_date']], 
            on='customer_id'
        )
        
        # Analyze by customer segment
        segment_analysis = customer_analysis.groupby('customer_segment').agg({
            'Total_Spent': ['mean', 'sum', 'count'],
            'Number_of_Orders': 'mean'
        }).round(2)
        
        print("👥 CUSTOMER SEGMENT ANALYSIS:")
        for segment in customer_analysis['customer_segment'].unique():
            segment_data = customer_analysis[customer_analysis['customer_segment'] == segment]
            avg_spent = segment_data['Total_Spent'].mean()
            total_customers = len(segment_data)
            avg_orders = segment_data['Number_of_Orders'].mean()
            
            print(f"  {segment} Customers:")
            print(f"    - Count: {total_customers}")
            print(f"    - Average Spent: ${avg_spent:.2f}")
            print(f"    - Average Orders: {avg_orders:.1f}")
        
        # Top spending customers
        top_customers = customer_analysis.nlargest(10, 'Total_Spent')
        print(f"\n💎 TOP 10 CUSTOMERS BY SPENDING:")
        for idx, (customer_id, row) in enumerate(top_customers.iterrows(), 1):
            print(f"  {idx}. Customer {customer_id}: ${row['Total_Spent']:,.2f} ({row['Number_of_Orders']} orders)")
        
        return customer_analysis
    
    def analyze_employee_performance(self):
        """
        Analyze sales employee performance
        Helps with performance reviews and incentives
        """
        print("\n" + "="*50)
        print("EMPLOYEE PERFORMANCE ANALYSIS")
        print("="*50)
        
        # Get sales employees (assuming department_id 1 is Sales)
        sales_employees = self.employees[self.employees['department_id'] == 1]
        
        # Calculate sales performance by employee
        employee_sales = self.sales.groupby('employee_id').agg({
            'total_amount': 'sum',
            'sale_id': 'count'
        }).round(2)
        
        employee_sales.columns = ['Total_Sales_Revenue', 'Number_of_Sales']
        
        # Merge with employee information
        employee_performance = employee_sales.merge(
            sales_employees[['employee_id', 'first_name', 'last_name', 'position', 'salary']], 
            on='employee_id'
        )
        
        # Sort by total sales revenue
        employee_performance = employee_performance.sort_values('Total_Sales_Revenue', ascending=False)
        
        print("🏆 TOP PERFORMING SALES EMPLOYEES:")
        for idx, (_, row) in enumerate(employee_performance.head(10).iterrows(), 1):
            name = f"{row['first_name']} {row['last_name']}"
            print(f"  {idx}. {name} ({row['position']}): ${row['Total_Sales_Revenue']:,.2f} ({row['Number_of_Sales']} sales)")
        
        return employee_performance
    
    def generate_summary_report(self):
        """
        Generate a comprehensive summary report
        Perfect for executive presentations
        """
        print("\n" + "="*60)
        print("EXECUTIVE SUMMARY REPORT")
        print("="*60)
        
        # Get all key metrics
        sales_overview = self.get_sales_overview()
        monthly_sales = self.analyze_sales_by_month()
        category_performance, product_performance = self.analyze_product_performance()
        customer_analysis = self.analyze_customer_segments()
        employee_performance = self.analyze_employee_performance()
        
        # Save summary to file
        with open('output/executive_summary.txt', 'w') as f:
            f.write("COMPANY PERFORMANCE EXECUTIVE SUMMARY\n")
            f.write("="*50 + "\n\n")
            f.write(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"Total Revenue: ${sales_overview['total_revenue']:,.2f}\n")
            f.write(f"Total Transactions: {sales_overview['total_transactions']:,}\n")
            f.write(f"Average Order Value: ${sales_overview['average_order_value']:.2f}\n")
            f.write(f"Daily Average Revenue: ${sales_overview['daily_average_revenue']:.2f}\n\n")
            f.write("Top Product Category: " + category_performance.index[0] + "\n")
            f.write("Top Product: " + product_performance.index[0] + "\n")
        
        print("\n✅ Executive summary saved to 'output/executive_summary.txt'")
        print("✅ Analysis complete! All key business metrics have been calculated.")

if __name__ == "__main__":
    # Create analyzer instance and run all analyses
    analyzer = CompanyDataAnalyzer()
    analyzer.generate_summary_report()
